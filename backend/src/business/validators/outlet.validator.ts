import { z } from 'zod';

// Base outlet name validation
const outletNameSchema = z
  .string()
  .min(1, 'Outlet name is required')
  .max(255, 'Outlet name must be less than 255 characters')
  .trim();

// Address validation
const addressSchema = z
  .string()
  .min(1, 'Address is required')
  .max(500, 'Address must be less than 500 characters')
  .trim();

// Phone number validation
const phoneNumberSchema = z
  .string()
  .min(1, 'Phone number is required')
  .max(20, 'Phone number must be less than 20 characters')
  .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format');

// Time validation (HH:MM format)
const timeSchema = z
  .string()
  .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Time must be in HH:MM format (24-hour)');

// Description validation
const descriptionSchema = z
  .string()
  .max(1000, 'Description must be less than 1000 characters')
  .optional();

// Boolean validation for active status
const isActiveSchema = z.boolean().default(true);

// Create outlet validation
export const createOutletSchema = z.object({
  name: outletNameSchema,
  address: addressSchema,
  phoneNumber: phoneNumberSchema,
  openingTime: timeSchema,
  closingTime: timeSchema,
  isActive: isActiveSchema,
  description: descriptionSchema,
}).refine((data) => {
  if (!data.openingTime || !data.closingTime) return true;
  const [openHour, openMin] = data.openingTime.split(':').map(Number);
  const [closeHour, closeMin] = data.closingTime.split(':').map(Number);
  if (openHour === undefined || openMin === undefined || closeHour === undefined || closeMin === undefined) {
    return false;
  }
  const openMinutes = openHour * 60 + openMin;
  const closeMinutes = closeHour * 60 + closeMin;
  return closeMinutes > openMinutes;
}, {
  message: 'Closing time must be after opening time',
  path: ['closingTime'],
});

// Update outlet validation (all fields optional except validation rules)
export const updateOutletSchema = z.object({
  name: outletNameSchema.optional(),
  address: addressSchema.optional(),
  phoneNumber: phoneNumberSchema.optional(),
  openingTime: timeSchema.optional(),
  closingTime: timeSchema.optional(),
  isActive: z.boolean().optional(),
  description: descriptionSchema,
}).refine((data) => {
  if (!data.openingTime || !data.closingTime) return true;
  const [openHour, openMin] = data.openingTime.split(':').map(Number);
  const [closeHour, closeMin] = data.closingTime.split(':').map(Number);
  if (openHour === undefined || openMin === undefined || closeHour === undefined || closeMin === undefined) {
    return false;
  }
  const openMinutes = openHour * 60 + openMin;
  const closeMinutes = closeHour * 60 + closeMin;
  return closeMinutes > openMinutes;
}, {
  message: 'Closing time must be after opening time',
  path: ['closingTime'],
});

// Outlet ID parameter validation
export const outletIdParamSchema = z.object({
  id: z.preprocess(
    (val) => parseInt(String(val), 10),
    z.number().positive('Outlet ID must be a positive number'),
  ),
});

// Outlet query parameters validation
export const outletQuerySchema = z.object({
  page: z.preprocess((val) => Number(val), z.number().int().min(1)).default(1),
  limit: z.preprocess((val) => Number(val), z.number().int().min(1).max(100)).default(10),
  search: z.string().max(255).optional(),
  isActive: z.preprocess((val) => val === 'true', z.boolean()).optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  minRating: z.preprocess((val) => Number(val), z.number().min(0).max(5)).optional(),
  maxPrice: z.preprocess((val) => Number(val), z.number().positive()).optional(),
  cuisine: z.string().trim().optional(),
  includeDeleted: z.preprocess((val) => val === 'true', z.boolean()).optional(),
});

// Outlet search validation
export const outletSearchSchema = z.object({
  q: z.string().min(1, 'Search query is required').max(255),
  includeInactive: z.preprocess((val) => val === 'true', z.boolean()).default(false),
});

// Bulk operation validation
export const bulkOutletActionSchema = z.object({
  action: z.enum(['activate', 'deactivate', 'delete']),
  outletIds: z.array(z.number().positive()).min(1, 'At least one outlet ID is required'),
});

// Outlet status update validation
export const outletStatusSchema = z.object({
  isActive: z.boolean(),
});

// Type exports
export type CreateOutletRequest = z.infer<typeof createOutletSchema>;
export type UpdateOutletRequest = z.infer<typeof updateOutletSchema>;
export type OutletIdParam = z.infer<typeof outletIdParamSchema>;
export type OutletQuery = z.infer<typeof outletQuerySchema>;
export type OutletSearch = z.infer<typeof outletSearchSchema>;
export type BulkOutletAction = z.infer<typeof bulkOutletActionSchema>;
export type OutletStatus = z.infer<typeof outletStatusSchema>;
