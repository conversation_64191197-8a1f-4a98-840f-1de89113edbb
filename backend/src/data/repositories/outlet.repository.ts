import { eq, isNull, and, desc, asc, like, or, count, ne } from 'drizzle-orm';
import { db, type Database } from '../../infrastructure/db/client';
import {
  outlets,
  type Outlet as OutletEntity,
  type CreateOutletPayload,
  type UpdateOutletPayload,
  type ActiveOutlet as ActiveOutletEntity,
} from '../models/outlet.model';
import { dbLogger } from '../../infrastructure/logger/pino';
import { paginate } from '../../infrastructure/db/utils';
import {
  IOutletRepository,
  type Outlet,
  type ActiveOutlet,
  type OutletQueryParams,
  type CreateOutletData,
  type UpdateOutletData,
  type PaginatedResult,
} from '../../business/interfaces/repositories/IOutletRepository';

export class OutletRepository implements IOutletRepository {
  constructor(private readonly database: Database = db) { }

  // Entity to domain model mapping
  private mapToOutlet(entity: OutletEntity): Outlet {
    return {
      outletId: entity.outletId,
      name: entity.name,
      address: entity.address,
      phone: entity.phoneNumber,
      description: entity.description,
      isActive: entity.isActive,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
    };
  }

  private mapToActiveOutlet(entity: ActiveOutletEntity): ActiveOutlet {
    return {
      outletId: entity.outletId,
      name: entity.name,
      address: entity.address,
      isActive: entity.isActive,
    };
  }

  // Implementation of IOutletRepository interface
  async findAll(query: OutletQueryParams): Promise<PaginatedResult<Outlet>> {
    try {
      const conditions = [];

      if (query.isActive !== undefined) {
        conditions.push(eq(outlets.isActive, query.isActive));
      }

      if (query.search) {
        conditions.push(
          or(
            like(outlets.name, `%${query.search}%`),
            like(outlets.address, `%${query.search}%`),
            like(outlets.description, `%${query.search}%`),
          ),
        );
      }

      // Conditionally exclude soft deleted outlets
      if (!query.includeDeleted) {
        conditions.push(isNull(outlets.deletedAt));
      }

      const sortableColumns = {
        name: outlets.name,
        createdAt: outlets.createdAt,
        updatedAt: outlets.updatedAt,
      };
      const sortColumn = sortableColumns[query.sortBy || 'name'];

      const mainQuery = this.database
        .select()
        .from(outlets)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(
          query.sortOrder === 'desc'
            ? desc(sortColumn)
            : asc(sortColumn),
        );

      const countQuery = this.database
        .select({ count: count() })
        .from(outlets)
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      const result = await paginate(mainQuery, countQuery, {
        page: query.page || 1,
        limit: query.limit || 10,
      });

      return {
        data: (result.data as OutletEntity[]).map(entity => this.mapToOutlet(entity)),
        pagination: result.pagination,
      };
    } catch (error) {
      dbLogger.error('Error finding all outlets:', error);
      throw error;
    }
  }

  async create(outletData: CreateOutletData): Promise<Outlet> {
    try {
      dbLogger.info('Creating new outlet', { name: outletData.name });

      // Convert CreateOutletData to CreateOutletPayload
      const payload: CreateOutletPayload = {
        name: outletData.name,
        address: outletData.address,
        phoneNumber: outletData.phone || '',
        description: outletData.description,
        isActive: outletData.isActive ?? true,
        openingTime: '09:00', // Default values - these should be configurable
        closingTime: '22:00',
      };

      const result = await this.database
        .insert(outlets)
        .values(payload);

      const insertId = result[0].insertId;
      const createdOutlet = await this.findById(Number(insertId));
      if (!createdOutlet) {
        throw new Error('Failed to retrieve created outlet');
      }

      dbLogger.info('Outlet created successfully', { outletId: createdOutlet.outletId });
      return createdOutlet;
    } catch (error) {
      dbLogger.error('Error creating outlet:', error);
      throw error;
    }
  }

  async findById(id: number): Promise<Outlet | null> {
    try {
      const [outlet] = await this.database
        .select()
        .from(outlets)
        .where(eq(outlets.outletId, id))
        .limit(1);

      return outlet ? this.mapToOutlet(outlet) : null;
    } catch (error) {
      dbLogger.error('Error finding outlet by ID:', error);
      throw error;
    }
  }

  async findActiveById(id: number): Promise<ActiveOutlet | null> {
    try {
      const [outlet] = await this.database
        .select()
        .from(outlets)
        .where(and(
          eq(outlets.outletId, id),
          isNull(outlets.deletedAt),
          eq(outlets.isActive, true),
        ))
        .limit(1);

      return outlet as ActiveOutlet || null;
    } catch (error) {
      dbLogger.error('Error finding active outlet by ID:', error);
      throw error;
    }
  }



  async findAllActive(): Promise<ActiveOutlet[]> {
    try {
      const outletList = await this.database
        .select()
        .from(outlets)
        .where(and(
          isNull(outlets.deletedAt),
          eq(outlets.isActive, true),
        ))
        .orderBy(asc(outlets.name));

      return outletList.map(outlet => this.mapToActiveOutlet(outlet as ActiveOutletEntity));
    } catch (error) {
      dbLogger.error('Error finding all active outlets:', error);
      throw error;
    }
  }

  async findByName(name: string): Promise<Outlet | null> {
    try {
      const [outlet] = await this.database
        .select()
        .from(outlets)
        .where(eq(outlets.name, name))
        .limit(1);

      return outlet ? this.mapToOutlet(outlet) : null;
    } catch (error) {
      dbLogger.error('Error finding outlet by name:', error);
      throw error;
    }
  }

  async update(id: number, data: Partial<UpdateOutletData>): Promise<Outlet | null> {
    try {
      dbLogger.info('Updating outlet', { outletId: id });

      // Convert UpdateOutletData to database format
      const updateData: Partial<UpdateOutletPayload> = {};
      if (data.name !== undefined) updateData.name = data.name;
      if (data.address !== undefined) updateData.address = data.address;
      if (data.phone !== undefined) updateData.phoneNumber = data.phone;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.isActive !== undefined) updateData.isActive = data.isActive;

      await this.database
        .update(outlets)
        .set(updateData)
        .where(eq(outlets.outletId, id));

      const updatedOutlet = await this.findById(id);
      if (updatedOutlet) {
        dbLogger.info('Outlet updated successfully', { outletId: id });
      }

      return updatedOutlet;
    } catch (error) {
      dbLogger.error('Error updating outlet:', error);
      throw error;
    }
  }

  async softDelete(id: number): Promise<boolean> {
    try {
      dbLogger.info('Soft deleting outlet', { outletId: id });

      const result = await this.database
        .update(outlets)
        .set({
          deletedAt: new Date(),
          isActive: false,
        })
        .where(eq(outlets.outletId, id));

      const success = result[0].affectedRows > 0;
      if (success) {
        dbLogger.info('Outlet soft deleted successfully', { outletId: id });
      }

      return success;
    } catch (error) {
      dbLogger.error('Error soft deleting outlet:', error);
      throw error;
    }
  }

  async restore(id: number): Promise<boolean> {
    try {
      dbLogger.info('Restoring outlet', { outletId: id });

      const result = await this.database
        .update(outlets)
        .set({
          deletedAt: null,
          isActive: true,
        })
        .where(eq(outlets.outletId, id));

      const success = result[0].affectedRows > 0;
      if (success) {
        dbLogger.info('Outlet restored successfully', { outletId: id });
      }

      return success;
    } catch (error) {
      dbLogger.error('Error restoring outlet:', error);
      throw error;
    }
  }

  async getStats(): Promise<any> {
    try {
      dbLogger.info('Fetching outlet stats');
      const totalOutlets = await this.database.select({ count: count() }).from(outlets);
      const activeOutlets = await this.database.select({ count: count() }).from(outlets).where(eq(outlets.isActive, true));
      // NOTE: These are dummy stats. In a real application, you'd have
      // more complex queries to calculate total and average revenue.
      return {
        totalOutlets: totalOutlets[0].count,
        activeOutlets: activeOutlets[0].count,
        totalRevenue: 1250000,
        averageRevenue: 25000,
      };
    } catch (error) {
      dbLogger.error('Error fetching outlet stats:', error);
      throw error;
    }
  }

  async hardDelete(id: number): Promise<boolean> {
    try {
      dbLogger.info('Hard deleting outlet', { outletId: id });

      const result = await this.database
        .delete(outlets)
        .where(eq(outlets.outletId, id));

      const success = result[0].affectedRows > 0;
      if (success) {
        dbLogger.info('Outlet hard deleted successfully', { outletId: id });
      }

      return success;
    } catch (error) {
      dbLogger.error('Error hard deleting outlet:', error);
      throw error;
    }
  }

  async nameExists(name: string, excludeId?: number): Promise<boolean> {
    try {
      const conditions = [eq(outlets.name, name)];

      if (excludeId) {
        // If excludeId is provided, we want to exclude that outlet from the check
        conditions.push(ne(outlets.outletId, excludeId));
      }

      const [outlet] = await this.database
        .select()
        .from(outlets)
        .where(conditions.length > 1 ? and(...conditions) : conditions[0])
        .limit(1);

      return !!outlet;
    } catch (error) {
      dbLogger.error('Error checking if outlet name exists:', error);
      throw error;
    }
  }
}

export const outletRepository = new OutletRepository();
