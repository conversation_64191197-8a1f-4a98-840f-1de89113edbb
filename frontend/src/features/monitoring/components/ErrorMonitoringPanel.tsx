import React, { useState, useEffect } from 'react';
import { errorReporting, ErrorReport } from '@/lib/monitoring/errorReporting';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { <PERSON><PERSON> } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs';
import { 
  AlertTriangle, 
  Bug, 
  Wifi, 
  Shield, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  Trash2
} from 'lucide-react';

const severityColors = {
  low: 'bg-blue-100 text-blue-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  critical: 'bg-red-100 text-red-800',
};

const typeIcons = {
  api_error: <Bug className="h-4 w-4" />,
  auth_error: <Shield className="h-4 w-4" />,
  network_error: <Wifi className="h-4 w-4" />,
  runtime_error: <AlertTriangle className="h-4 w-4" />,
  validation_error: <XCircle className="h-4 w-4" />,
};

export function ErrorMonitoringPanel() {
  const [errors, setErrors] = useState<ErrorReport[]>([]);
  const [stats, setStats] = useState<any>({});
  const [selectedError, setSelectedError] = useState<ErrorReport | null>(null);

  const refreshData = () => {
    setErrors(errorReporting.getErrors());
    setStats(errorReporting.getErrorStats());
  };

  useEffect(() => {
    refreshData();
    
    // Refresh every 5 seconds
    const interval = setInterval(refreshData, 5000);
    return () => clearInterval(interval);
  }, []);

  const handleResolveError = (errorId: string) => {
    errorReporting.resolveError(errorId);
    refreshData();
  };

  const handleClearErrors = () => {
    errorReporting.clearErrors();
    refreshData();
    setSelectedError(null);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const ErrorCard = ({ error }: { error: ErrorReport }) => (
    <Card 
      className={`cursor-pointer transition-colors hover:bg-gray-50 ${
        selectedError?.id === error.id ? 'ring-2 ring-blue-500' : ''
      }`}
      onClick={() => setSelectedError(error)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="mt-1">
              {typeIcons[error.type]}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <Badge variant="outline" className={severityColors[error.severity]}>
                  {error.severity}
                </Badge>
                <Badge variant="secondary">
                  {error.type.replace('_', ' ')}
                </Badge>
                {error.resolved && (
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Resolved
                  </Badge>
                )}
              </div>
              <p className="text-sm font-medium text-gray-900 truncate">
                {error.message}
              </p>
              <p className="text-xs text-gray-500">
                {formatTimestamp(error.createdAt)}
              </p>
              {error.apiDetails && (
                <p className="text-xs text-gray-600 mt-1">
                  {error.apiDetails.method} {error.apiDetails.endpoint}
                  {error.apiDetails.status && ` (${error.apiDetails.status})`}
                </p>
              )}
            </div>
          </div>
          {!error.resolved && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                handleResolveError(error.id);
              }}
            >
              Resolve
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const ErrorDetails = ({ error }: { error: ErrorReport }) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          {typeIcons[error.type]}
          <span>Error Details</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-medium mb-2">Message</h4>
          <p className="text-sm text-gray-700">{error.message}</p>
        </div>
        
        {error.stack && (
          <div>
            <h4 className="font-medium mb-2">Stack Trace</h4>
            <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto">
              {error.stack}
            </pre>
          </div>
        )}

        {error.apiDetails && (
          <div>
            <h4 className="font-medium mb-2">API Details</h4>
            <div className="text-sm space-y-1">
              <p><strong>Endpoint:</strong> {error.apiDetails.endpoint}</p>
              <p><strong>Method:</strong> {error.apiDetails.method}</p>
              {error.apiDetails.status && (
                <p><strong>Status:</strong> {error.apiDetails.status} {error.apiDetails.statusText}</p>
              )}
              {error.apiDetails.responseData && (
                <div>
                  <strong>Response:</strong>
                  <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                    {JSON.stringify(error.apiDetails.responseData, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        <div>
          <h4 className="font-medium mb-2">Context</h4>
          <div className="text-sm space-y-1">
            <p><strong>URL:</strong> {error.context.url}</p>
            <p><strong>Timestamp:</strong> {formatTimestamp(error.context.timestamp || error.createdAt)}</p>
            <p><strong>Session ID:</strong> {error.context.sessionId}</p>
            {error.context.userId && (
              <p><strong>User ID:</strong> {error.context.userId}</p>
            )}
            {error.context.userEmail && (
              <p><strong>User Email:</strong> {error.context.userEmail}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Error Monitoring</h2>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={refreshData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm" onClick={handleClearErrors}>
            <Trash2 className="h-4 w-4 mr-2" />
            Clear All
          </Button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stats.total || 0}</div>
            <p className="text-sm text-gray-600">Total Errors</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats.unresolved || 0}</div>
            <p className="text-sm text-gray-600">Unresolved</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">
              {stats.bySeverity?.critical || 0}
            </div>
            <p className="text-sm text-gray-600">Critical</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {stats.bySeverity?.high || 0}
            </div>
            <p className="text-sm text-gray-600">High Severity</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Error List */}
        <div>
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="unresolved">Unresolved</TabsTrigger>
              <TabsTrigger value="api">API</TabsTrigger>
              <TabsTrigger value="auth">Auth</TabsTrigger>
            </TabsList>
            
            <TabsContent value="all" className="space-y-3 mt-4">
              {errors.map(error => (
                <ErrorCard key={error.id} error={error} />
              ))}
            </TabsContent>
            
            <TabsContent value="unresolved" className="space-y-3 mt-4">
              {errorReporting.getUnresolvedErrors().map(error => (
                <ErrorCard key={error.id} error={error} />
              ))}
            </TabsContent>
            
            <TabsContent value="api" className="space-y-3 mt-4">
              {errorReporting.getErrorsByType('api_error').map(error => (
                <ErrorCard key={error.id} error={error} />
              ))}
            </TabsContent>
            
            <TabsContent value="auth" className="space-y-3 mt-4">
              {errorReporting.getErrorsByType('auth_error').map(error => (
                <ErrorCard key={error.id} error={error} />
              ))}
            </TabsContent>
          </Tabs>
        </div>

        {/* Error Details */}
        <div>
          {selectedError ? (
            <ErrorDetails error={selectedError} />
          ) : (
            <Card>
              <CardContent className="p-8 text-center text-gray-500">
                Select an error to view details
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
