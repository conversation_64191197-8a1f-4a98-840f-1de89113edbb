import React from 'react';
import { useAdminDashboard } from '../hooks/useAdminDashboard';
import { MetricCard } from '../components/shared/MetricCard';
import { ChartWidget } from '../components/shared/ChartWidget';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/shared/components/ui/collapsible';
import { Button } from '@/shared/components/ui/button';
import { formatCurrency } from '@/shared/utils/format';
import {
  DollarSign,
  Users,
  Building2,
  TrendingUp,
  Activity,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';
import { AdminDashboardData } from '../types';
import {
  DashboardErrorBoundary,
  DashboardLoadingSkeleton,
  QueryErrorDisplay,
} from '../components/shared/DashboardErrorBoundary';

import { useAuth } from '@/features/auth/hooks/useAuth';

function DashboardContent({ data }: { data: AdminDashboardData }) {
  const { metrics, salesTrend, outletPerformance, recentActivity, userActivity } = data;
  const [isRecentActivityOpen, setIsRecentActivityOpen] = React.useState(true);

  // Limit the number of notifications displayed (default to 5)
  const maxNotifications = 5;
  const displayedActivity = React.useMemo(() => {
    return recentActivity?.slice(0, maxNotifications) || [];
  }, [recentActivity, maxNotifications]);

  const metricCards = React.useMemo(
    () => [
      {
        title: 'Total Revenue',
        value: formatCurrency(metrics?.totalRevenue || 0),
        change: '+12.5%',
        changeDescription: 'from last month',
        icon: <DollarSign className="h-4 w-4" />,
      },
      {
        title: 'Total Sales',
        value: metrics?.totalSales || 0,
        change: '+8.2%',
        changeDescription: 'from last month',
        icon: <TrendingUp className="h-4 w-4" />,
      },
      {
        title: 'Active Users',
        value: metrics?.activeUsers || 0,
        change: `${userActivity?.find(u => u.name === 'Active Users')?.percentage || 0}%`,
        changeDescription: 'of total users',
        icon: <Users className="h-4 w-4" />,
      },
      {
        title: 'Outlets',
        value: metrics?.totalOutlets || 0,
        change: '+2.3%',
        changeDescription: 'from last month',
        icon: <Building2 className="h-4 w-4" />,
      },
    ],
    [metrics, userActivity]
  );

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <p className="text-muted-foreground">
          Overview of your business performance and key metrics.
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metricCards.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <ChartWidget
          title="Sales Trend"
          description="Sales performance over time"
          data={salesTrend || []}
          type="line"
          xAxisKey="name"
          dataKeys={['value']}
        />
        <ChartWidget
          title="Outlet Performance"
          description="Top performing outlets"
          data={outletPerformance || []}
          type="bar"
          xAxisKey="name"
          dataKeys={['value']}
        />
      </div>

      <Card>
        <Collapsible open={isRecentActivityOpen} onOpenChange={setIsRecentActivityOpen}>
          <CardHeader>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className="flex w-full items-center justify-between p-0 hover:bg-transparent"
              >
                <CardTitle className="flex items-center">
                  <Activity className="mr-2 h-5 w-5" />
                  Recent Activity
                  {displayedActivity.length > 0 && (
                    <span className="bg-muted ml-2 rounded-full px-2 py-1 text-xs font-normal">
                      {displayedActivity.length}
                      {recentActivity && recentActivity.length > maxNotifications && '+'}
                    </span>
                  )}
                </CardTitle>
                {isRecentActivityOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
          </CardHeader>
          <CollapsibleContent>
            <CardContent>
              <div className="space-y-4">
                {displayedActivity.length > 0 ? (
                  displayedActivity.map(activity => (
                    <div key={activity.id} className="flex items-center space-x-4">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.message}</p>
                        <p className="text-muted-foreground text-xs">
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                        {activity.outletName && (
                          <p className="text-muted-foreground text-xs">
                            Outlet: {activity.outletName}
                          </p>
                        )}
                      </div>
                      {activity.amount && (
                        <div className="text-sm font-medium">{formatCurrency(activity.amount)}</div>
                      )}
                    </div>
                  ))
                ) : (
                  <p className="text-muted-foreground">No recent activity</p>
                )}
                {recentActivity && recentActivity.length > maxNotifications && (
                  <div className="border-t pt-2">
                    <p className="text-muted-foreground text-center text-xs">
                      Showing {maxNotifications} of {recentActivity.length} activities
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
}

export function AdminDashboardPage(): React.ReactElement {
  const { isAuthenticated } = useAuth();

  // Default query parameters for admin dashboard
  const defaultQuery = React.useMemo(
    () => ({
      period: 'month' as const,
      includeDetails: true,
      limit: 10,
    }),
    []
  );

  const {
    data: dashboardData,
    isLoading,
    error,
    refetch,
    isError,
  } = useAdminDashboard(defaultQuery);

  const handleRetry = React.useCallback(() => {
    refetch();
  }, [refetch]);

  // Handle authentication errors specifically
  const isAuthError = error?.response?.status === 401 || error?.status === 401;

  if (isAuthError && !isAuthenticated) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Authentication required to access admin dashboard.
          </p>
        </div>
        <QueryErrorDisplay error={error} onRetry={handleRetry} className="mx-auto max-w-md" />
      </div>
    );
  }

  return (
    <DashboardErrorBoundary>
      {isLoading && <DashboardLoadingSkeleton />}
      {isError && !isAuthError && <QueryErrorDisplay error={error} onRetry={handleRetry} />}
      {dashboardData && !isLoading && !isError && <DashboardContent data={dashboardData} />}
    </DashboardErrorBoundary>
  );
}
