import React from 'react';
import type { MetricCardProps } from '../components/shared/MetricCard';

// Shared interfaces
export interface Metric {
  title: string;
  value: string | number;
  change: string;
  changeDescription: string;
  icon: React.ReactNode;
}

export interface ChartData {
  name: string;
  value: number;
}

export interface RecentActivity {
  id: string;
  message: string;
  timestamp: string;
  amount?: number;
}

// --- New Analysis Service Types ---

export interface MonthlyRevenue {
  month: string;
  monthName: string;
  year: number;
  totalRevenue: number;
  formattedRevenue: string;
  changePercentage: number | null;
  changeDirection: 'up' | 'down' | 'neutral' | null;
}

export interface BranchAnalytics {
  outletId: number;
  outletName: string;
  totalRevenue: number;
  formattedTotalRevenue: string;
  monthlyRevenue: MonthlyRevenue[];
  revenueStats: {
    averageMonthlyRevenue: number;
    highestMonth: { month: string; revenue: number } | null;
    lowestMonth: { month: string; revenue: number } | null;
    totalMonths: number;
  };
}

export interface KpiProgressItem {
  month: string;
  monthName: string;
  year: number;
  achievedAmount: number;
  formattedAchievedAmount: string;
  targetAmount: number;
  formattedTargetAmount: string;
  difference: number;
  formattedDifference: string;
  differencePercentage: number;
  formattedDifferencePercentage: string;
  status: 'above_target' | 'below_target' | 'on_target';
  progressPercentage: number;
}

export interface KpiProgress {
  outletId: number;
  outletName: string;
  kpiProgress: KpiProgressItem[];
  summary: {
    totalMonths: number;
    monthsAboveTarget: number;
    monthsBelowTarget: number;
    averageAchievement: number;
    overallProgressPercentage: number;
  };
  monthlyTarget: number;
  formattedMonthlyTarget: string;
}

export interface OverallRevenueData {
  overview: {
    actualRevenue: number;
    forecastRevenue: number;
    achievementPercentage: number;
  };
  chartData: Array<{ month: string; actual: number; forecast: number }>;
  performanceData: Array<{
    outlet: string;
    forecast: number;
    target: number;
    actual: number;
    risk: 'low' | 'medium' | 'high';
    achievement: number;
    trend: 'up' | 'down' | 'neutral';
  }>;
}

// Backend API Response Types
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

// Dashboard Types matching backend validators
export interface DashboardMetrics {
  totalUsers: number;
  totalOutlets: number;
  totalSales: number;
  totalRevenue: number;
  activeUsers: number;
  inactiveUsers: number;
  newUsersThisMonth: number;
  recentSales: Array<{
    id: number;
    amount: number;
    date: string;
    outletName: string;
  }>;
  topoutlets: Array<{
    id: number;
    name: string;
    totalRevenue: number;
    salesCount: number;
  }>;
}

export interface SalesTrendData {
  name: string;
  value: number;
  date: string;
}

export interface outletPerformance {
  name: string;
  value: number;
  outletId: number;
  salesCount: number;
  averageSale: number;
}

export interface UserActivityData {
  name: string;
  value: number;
  percentage: number;
}

export interface RecentActivityItem {
  id: string;
  type: 'user' | 'sale' | 'outlet' | 'system';
  message: string;
  timestamp: string;
  user?: string;
  amount?: number;
  outletId?: number;
  outletName?: string;
}

// Admin Dashboard Data Structure
export interface AdminDashboardData {
  metrics: DashboardMetrics;
  salesTrend: SalesTrendData[];
  outletPerformance: outletPerformance[];
  userActivity: UserActivityData[];
  recentActivity: RecentActivityItem[];
}

// Query Parameter Types
export interface AdminDashboardQuery {
  period?: 'today' | 'week' | 'month' | 'quarter' | 'year' | '6m' | '12m' | '24m';
  includeDetails?: boolean;
  limit?: number;
}

export interface RecentActivityQuery {
  limit?: number;
  type?: 'all' | 'user' | 'sale' | 'outlet';
  outletId?: number;
}

// API Response Types
export type AdminDashboardResponse = ApiResponse<AdminDashboardData>;
export type DashboardMetricsResponse = ApiResponse<{
  metrics: DashboardMetrics;
  userActivity: UserActivityData[];
}>;
export type RecentActivityResponse = ApiResponse<{
  activities: RecentActivityItem[];
  total: number;
}>;
export type SalesTrendResponse = ApiResponse<{
  salesTrend: SalesTrendData[];
  outletPerformance: outletPerformance[];
}>;

// Legacy type for backward compatibility - can be removed after full refactor
export interface LegacyAdminDashboardData {
  metrics: {
    totalRevenue: number;
    totalSales: number;
    totalUsers: number;
    totaloutlets: number;
  };
  salesTrend: ChartData[];
  outletPerformance: ChartData[];
  recentActivity: RecentActivity[];
}

// User Performance Data
export interface UserPerformance {
  thisWeek?: number;
  lastWeek?: number;
  change?: number;
  changeDirection?: 'up' | 'down' | 'neutral';
}

// User Dashboard Data (matching backend response)
export interface UserDashboardData {
  todaySales: number;
  weeklySales: number;
  monthlySales: number;
  salesTarget: number;
  targetAchievement: number;
  recentSales: Array<{
    id: number;
    amount: number;
    date: string;
    outletName: string;
  }>;
  performance: {
    thisWeek: number;
    lastWeek: number;
    change: number;
    changeDirection: 'up' | 'down' | 'neutral';
  };
}

// Legacy User Dashboard Data (for backward compatibility)
export interface LegacyUserDashboardData {
  metrics: {
    personalSales: number;
    outletSales: number;
    targetProgress: number;
  };
  salesTrend: ChartData[];
  recentSales: RecentActivity[];
  kpi: {
    target: number;
    achieved: number;
  };
}

export interface SalesTrendChartCardProps {
  salesTrendView: string;
  onSalesTrendViewChange: (view: string) => void;
  displayedSalesTrendData: ChartData[];
  trendOptions: string[];
}

// Sales Breakdown Chart Data Structure
export interface SalesBreakdownData {
  name: string;
  value: number;
  rawValue?: number;
}

export interface SalesBreakdownChartCardProps {
  breakdownView: string;
  onBreakdownViewChange: (view: string) => void;
  displayedSalesBreakdownData: SalesBreakdownData[];
  breakdownOptions: string[];
  pieInnerRadius: number;
  pieOuterRadius: number;
}

// User Metrics Response Type
export interface UserMetricsData {
  metrics: MetricCardProps[];
}

// API Response Types for User Dashboard
export type UserDashboardResponse = ApiResponse<UserDashboardData>;
export type UserMetricsResponse = ApiResponse<UserMetricsData>;
export type UserSalesTrendResponse = ApiResponse<ChartData[]>;
export type UserSalesBreakdownResponse = ApiResponse<SalesBreakdownData[]>;
