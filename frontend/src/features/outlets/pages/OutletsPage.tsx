import { useState, useCallback } from 'react';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Plus, RefreshCw } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/ui/dialog';
import {
  useOutlets,
  useOutletStatsSummary,
  useBulkDeleteOutlets,
  useCreateOutlet,
  useUpdateOutlet,
  useDeleteOutlet,
} from '../hooks/useOutlets';
import { OutletTable } from '../components/OutletTable';
import { OutletStatsCards } from '../components/OutletStatsCards';
import { OutletForm } from '../components/OutletForm';
import { OutletDeleteDialog } from '../components/OutletDeleteDialog';
import { OutletDetailsDialog } from '../components/OutletDetailsDialog';
import {
  OutletLoadingState,
  OutletErrorState,
  OutletEmptyState,
  RefreshingOutletWrapper,
} from '../components/OutletLoadingStates';
import {
  OutletFilters,
  OutletWithManager,
  CreateOutletRequest,
  UpdateOutletRequest,
} from '../types';

export function OutletsPage() {
  // State for filters and pagination
  const [filters, setFilters] = useState<OutletFilters>({});
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  // State for modals and dialogs
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [editingOutlet, setEditingOutlet] = useState<OutletWithManager | null>(null);
  const [deletingOutlet, setDeletingOutlet] = useState<OutletWithManager | null>(null);
  const [viewingOutlet, setViewingOutlet] = useState<OutletWithManager | null>(null);

  // API hooks
  const {
    data: outletsResponse,
    isLoading: outletsLoading,
    error: outletsError,
    refetch: refetchOutlets,
    isFetching: outletsRefetching,
  } = useOutlets({
    ...filters,
    page: pagination.pageIndex + 1,
    limit: pagination.pageSize,
  });

  const {
    data: statsData,
    isLoading: statsLoading,
    refetch: refetchStats,
  } = useOutletStatsSummary();

  // Mutation hooks
  const createOutletMutation = useCreateOutlet();
  const updateOutletMutation = useUpdateOutlet();
  const deleteOutletMutation = useDeleteOutlet();
  const bulkDeleteMutation = useBulkDeleteOutlets();

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: OutletFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, pageIndex: 0 })); // Reset to first page
  }, []);

  // Handle pagination changes
  const handlePaginationChange = useCallback(
    (newPagination: { pageIndex: number; pageSize: number }) => {
      setPagination(newPagination);
    },
    []
  );

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetchOutlets();
    refetchStats();
  }, [refetchOutlets, refetchStats]);

  // Handle outlet actions
  const handleCreateOutlet = useCallback(() => {
    setShowCreateDialog(true);
  }, []);

  const handleEditOutlet = useCallback((outlet: OutletWithManager) => {
    setEditingOutlet(outlet);
    setShowEditDialog(true);
  }, []);

  const handleDeleteOutlet = useCallback((outlet: OutletWithManager) => {
    setDeletingOutlet(outlet);
    setShowDeleteDialog(true);
  }, []);

  const handleViewOutlet = useCallback((outlet: OutletWithManager) => {
    setViewingOutlet(outlet);
    setShowDetailsDialog(true);
  }, []);

  // Form submission handlers
  const handleCreateSubmit = useCallback(
    async (data: CreateOutletRequest | UpdateOutletRequest) => {
      await createOutletMutation.mutateAsync(data as CreateOutletRequest);
      setShowCreateDialog(false);
      refetchOutlets();
      refetchStats();
    },
    [createOutletMutation, refetchOutlets, refetchStats]
  );

  const handleUpdateSubmit = useCallback(
    async (data: CreateOutletRequest | UpdateOutletRequest) => {
      if (!editingOutlet) return;
      await updateOutletMutation.mutateAsync({
        id: editingOutlet.id.toString(),
        data: data as UpdateOutletRequest,
      });
      setShowEditDialog(false);
      setEditingOutlet(null);
      refetchOutlets();
      refetchStats();
    },
    [editingOutlet, updateOutletMutation, refetchOutlets, refetchStats]
  );

  const handleConfirmDelete = useCallback(async () => {
    if (!deletingOutlet) return;
    await deleteOutletMutation.mutateAsync(deletingOutlet.id.toString());
    setShowDeleteDialog(false);
    setDeletingOutlet(null);
    refetchOutlets();
    refetchStats();
  }, [deletingOutlet, deleteOutletMutation, refetchOutlets, refetchStats]);

  const handleBulkDelete = useCallback(
    (outlets: OutletWithManager[]) => {
      const outletIds = outlets.map(outlet => outlet.id.toString());
      bulkDeleteMutation.mutate(outletIds, {
        onSuccess: () => {
          refetchOutlets();
          refetchStats();
        },
      });
    },
    [bulkDeleteMutation, refetchOutlets, refetchStats]
  );

  // Log API data and errors for debugging
  if (outletsResponse) {
    console.log('Outlets Response:', outletsResponse);
  }
  if (outletsError) {
    console.error('Outlets Error:', outletsError);
  }

  // Show main loading state on initial load
  if (outletsLoading && !outletsResponse) {
    return <OutletLoadingState message="Loading outlets..." />;
  }

  // Show error state if there's an error and no cached data
  if (outletsError && !outletsResponse) {
    return (
      <OutletErrorState error={outletsError} onRetry={handleRefresh} className="min-h-[600px]" />
    );
  }

  // Ensure outletsResponse is defined before destructuring
  if (!outletsResponse) {
    return (
      <OutletErrorState
        error={new Error('Failed to load outlets data.')}
        onRetry={handleRefresh}
        className="min-h-[600px]"
      />
    );
  }

  // Extract data from response
  const { outlets = [], total: totalItems = 0, totalPages = 0 } = outletsResponse;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Outlet Management</h1>
          <p className="text-muted-foreground">
            Manage outlets and their information. Total: {totalItems} outlets
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={outletsRefetching}
            className="gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${outletsRefetching ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleCreateOutlet} className="gap-2">
            <Plus className="h-4 w-4" />
            Add Outlet
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <OutletStatsCards stats={statsData} loading={statsLoading} />

      {/* Main Content */}
      {outlets.length === 0 && !outletsLoading ? (
        <OutletEmptyState
          title="No Outlets Found"
          message={
            Object.keys(filters).length > 0
              ? 'No outlets match your current filters. Try adjusting your search criteria.'
              : 'No outlets have been created yet. Create your first outlet to get started.'
          }
          onCreateOutlet={handleCreateOutlet}
        />
      ) : (
        <RefreshingOutletWrapper isRefreshing={outletsRefetching}>
          <OutletTable
            outlets={outlets}
            loading={outletsLoading}
            onEdit={handleEditOutlet}
            onDelete={handleDeleteOutlet}
            onView={handleViewOutlet}
            onBulkDelete={handleBulkDelete}
            onFiltersChange={handleFiltersChange}
            filters={filters}
            pagination={{
              pageIndex: pagination.pageIndex,
              pageSize: pagination.pageSize,
              totalPages,
              totalItems,
            }}
            onPaginationChange={handlePaginationChange}
          />
        </RefreshingOutletWrapper>
      )}

      {/* Modal Dialogs */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto sm:max-h-[85vh]">
          <DialogHeader>
            <DialogTitle>Create New Outlet</DialogTitle>
          </DialogHeader>
          <OutletForm
            isOpen={showCreateDialog}
            onClose={() => setShowCreateDialog(false)}
            onSubmit={handleCreateSubmit}
            loading={createOutletMutation.isPending}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto sm:max-h-[85vh]">
          <DialogHeader>
            <DialogTitle>Edit Outlet</DialogTitle>
          </DialogHeader>
          <OutletForm
            outlet={editingOutlet}
            isOpen={showEditDialog}
            onClose={() => {
              setShowEditDialog(false);
              setEditingOutlet(null);
            }}
            onSubmit={handleUpdateSubmit}
            loading={updateOutletMutation.isPending}
          />
        </DialogContent>
      </Dialog>

      <OutletDeleteDialog
        outlet={deletingOutlet}
        open={showDeleteDialog}
        onConfirm={handleConfirmDelete}
        onCancel={() => {
          setShowDeleteDialog(false);
          setDeletingOutlet(null);
        }}
        loading={deleteOutletMutation.isPending}
      />

      <OutletDetailsDialog
        outlet={viewingOutlet}
        open={showDetailsDialog}
        onClose={() => {
          setShowDetailsDialog(false);
          setViewingOutlet(null);
        }}
        onEdit={handleEditOutlet}
        onDelete={handleDeleteOutlet}
      />
    </div>
  );
}
