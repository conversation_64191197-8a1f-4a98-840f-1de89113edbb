import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { AlertCircle, Building2, MapPin, Phone, FileText, User } from 'lucide-react';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Textarea } from '@/shared/components/ui/textarea';
import { Switch } from '@/shared/components/ui/switch';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/shared/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { outletFormSchema, outletUpdateSchema } from '../validations/restaurantSchemas';
import { CreateOutletRequest, UpdateOutletRequest, OutletWithManager } from '../types';
import { useUsers } from '@/features/user-management/hooks/useUserQueries';
import { toast } from '@/shared/components/ui/use-toast';

interface OutletFormProps {
  outlet?: OutletWithManager | null;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateOutletRequest | UpdateOutletRequest) => Promise<void>;
  loading?: boolean;
}

type OutletFormData = CreateOutletRequest & {
  isActive?: boolean;
  openingTime?: string;
  closingTime?: string;
  description?: string;
};

export function OutletForm({
  outlet,
  isOpen,
  onClose,
  onSubmit,
  loading = false,
}: OutletFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const isEditing = !!outlet;
  const schema = isEditing ? outletUpdateSchema : outletFormSchema;

  // Fetch users for manager selection
  const {
    data: usersResponse,
    isLoading: usersLoading,
    error: usersError,
  } = useUsers({ limit: 1000 }); // Get all users for manager selection

  const users = usersResponse?.users || [];

  const form = useForm<OutletFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      address: '',
      phone: '',
      email: '',
      managerId: '',
      isActive: true,
      openingTime: '09:00',
      closingTime: '22:00',
      description: '',
    },
    mode: 'onBlur',
  });

  const { handleSubmit, reset, control } = form;

  // Reset form when outlet changes or dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setSubmitError(null);
      if (outlet) {
        // Edit mode
        reset({
          name: outlet.name || '',
          address: outlet.address || '',
          phone: outlet.phoneNumber || '',
          email: outlet.email || '',
          managerId: outlet.manager?.userId?.toString() || '',
          isActive: outlet.isActive !== false,
          openingTime: outlet.openingTime || '09:00',
          closingTime: outlet.closingTime || '22:00',
          description: outlet.description || '',
        });
      } else {
        // Create mode
        reset({
          name: '',
          address: '',
          phone: '',
          email: '',
          managerId: '',
          isActive: true,
          openingTime: '09:00',
          closingTime: '22:00',
          description: '',
        });
      }
    }
  }, [outlet?.outletId, isOpen, reset]);

  const onFormSubmit = async (data: OutletFormData) => {
    try {
      setSubmitError(null);

      // Transform data for API
      const submitData = isEditing
        ? ({
            name: data.name,
            address: data.address,
            phone: data.phone,
            email: data.email || undefined,
            managerId: data.managerId || undefined,
            isActive: data.isActive,
          } as UpdateOutletRequest)
        : ({
            name: data.name,
            address: data.address,
            phone: data.phone,
            email: data.email || undefined,
            managerId: data.managerId || undefined,
          } as CreateOutletRequest);

      await onSubmit(submitData);
      // onClose() is called by parent component on success
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message || error?.message || 'An unexpected error occurred';
      setSubmitError(errorMessage);
      toast.error(errorMessage);
    }
  };

  // Filter users to show only staff and admin for manager selection
  const managerOptions = users.filter(user => user.role === 'staff' || user.role === 'admin');

  return (
    <div className="space-y-6">
      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-8">
          {/* Basic Information Section */}
          <div className="space-y-4">
            <h3 className="text-foreground text-lg font-medium leading-6">Basic Information</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <FormField
                control={control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      Outlet Name
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Enter outlet name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Phone Number
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Enter phone number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Address
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter outlet address"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Contact & Management Section */}
          <div className="space-y-4">
            <h3 className="text-foreground text-lg font-medium leading-6">Contact & Management</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <FormField
                control={control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email (Optional)</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="Enter email address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="managerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Manager (Optional)
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={usersLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a manager" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">No Manager</SelectItem>
                        {managerOptions.map(user => (
                          <SelectItem key={user.userId} value={user.userId.toString()}>
                            {user.fullName} ({user.role})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {usersError && (
                      <FormDescription className="text-destructive">
                        Failed to load users
                      </FormDescription>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="space-y-4">
            <h3 className="text-foreground text-lg font-medium leading-6">
              Additional Information
            </h3>

            <FormField
              control={control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Description (Optional)
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter outlet description"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {isEditing && (
              <FormField
                control={control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <FormDescription>Enable or disable this outlet</FormDescription>
                    </div>
                    <FormControl>
                      <Switch checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                  </FormItem>
                )}
              />
            )}
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 border-t pt-6">
            <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  {isEditing ? 'Updating...' : 'Creating...'}
                </>
              ) : isEditing ? (
                'Update Outlet'
              ) : (
                'Create Outlet'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
