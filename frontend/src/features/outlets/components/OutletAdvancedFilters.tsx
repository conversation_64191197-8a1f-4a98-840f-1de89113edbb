import { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Search, Filter, X, ChevronDown, ChevronUp, Calendar, User, RotateCcw } from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Badge } from '@/shared/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/shared/components/ui/collapsible';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover';
import { Calendar as CalendarComponent } from '@/shared/components/ui/calendar';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shared/components/ui/form';
import { cn } from '@/lib/utils';
import { OutletFilters } from '../types';
import { useUsers } from '@/features/user-management/hooks/useUserQueries';
import { format } from 'date-fns';

const advancedFiltersSchema = z.object({
  search: z.string().optional(),
  managerId: z.string().optional(),
  isActive: z.enum(['all', 'active', 'inactive']).optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  hasManager: z.enum(['all', 'yes', 'no']).optional(),
  hasEmail: z.enum(['all', 'yes', 'no']).optional(),
});

type AdvancedFiltersData = z.infer<typeof advancedFiltersSchema>;

interface OutletAdvancedFiltersProps {
  filters: OutletFilters;
  onFiltersChange: (filters: OutletFilters) => void;
  className?: string;
}

export function OutletAdvancedFilters({
  filters,
  onFiltersChange,
  className,
}: OutletAdvancedFiltersProps) {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [showDateFromPicker, setShowDateFromPicker] = useState(false);
  const [showDateToPicker, setShowDateToPicker] = useState(false);

  // Fetch users for manager filter
  const { data: usersResponse, isLoading: usersLoading } = useUsers({ limit: 1000 });

  const users = usersResponse?.users || [];
  const managerOptions = users.filter(user => user.role === 'staff' || user.role === 'admin');

  const form = useForm<AdvancedFiltersData>({
    resolver: zodResolver(advancedFiltersSchema),
    defaultValues: {
      search: filters.search || '',
      managerId: filters.managerId || '',
      isActive:
        filters.isActive === true ? 'active' : filters.isActive === false ? 'inactive' : 'all',
      dateFrom: filters.dateFrom ? new Date(filters.dateFrom) : undefined,
      dateTo: filters.dateTo ? new Date(filters.dateTo) : undefined,
      hasManager: 'all',
      hasEmail: 'all',
    },
  });

  const { watch, setValue, reset } = form;
  const watchedValues = watch();

  // Convert form data to OutletFilters
  const convertToOutletFilters = useCallback((data: AdvancedFiltersData): OutletFilters => {
    return {
      search: data.search || undefined,
      managerId: data.managerId || undefined,
      isActive:
        data.isActive === 'active' ? true : data.isActive === 'inactive' ? false : undefined,
      dateFrom: data.dateFrom ? format(data.dateFrom, 'yyyy-MM-dd') : undefined,
      dateTo: data.dateTo ? format(data.dateTo, 'yyyy-MM-dd') : undefined,
    };
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback(
    (field: keyof AdvancedFiltersData, value: any) => {
      setValue(field, value);
      const newData = { ...watchedValues, [field]: value };
      const newFilters = convertToOutletFilters(newData);
      onFiltersChange(newFilters);
    },
    [watchedValues, setValue, convertToOutletFilters, onFiltersChange]
  );

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    reset({
      search: '',
      managerId: '',
      isActive: 'all',
      dateFrom: undefined,
      dateTo: undefined,
      hasManager: 'all',
      hasEmail: 'all',
    });
    onFiltersChange({});
  }, [reset, onFiltersChange]);

  // Count active filters
  const activeFiltersCount = Object.values(filters).filter(
    value => value !== undefined && value !== '' && value !== 'all'
  ).length;

  // Get active filter badges
  const getActiveFilterBadges = () => {
    const badges = [];

    if (filters.search) {
      badges.push({ label: `Search: "${filters.search}"`, key: 'search' });
    }

    if (filters.isActive !== undefined) {
      badges.push({
        label: `Status: ${filters.isActive ? 'Active' : 'Inactive'}`,
        key: 'isActive',
      });
    }

    if (filters.managerId) {
      const manager = managerOptions.find(u => u.userId.toString() === filters.managerId);
      badges.push({
        label: `Manager: ${manager?.fullName || 'Unknown'}`,
        key: 'managerId',
      });
    }

    if (filters.dateFrom) {
      badges.push({ label: `From: ${filters.dateFrom}`, key: 'dateFrom' });
    }

    if (filters.dateTo) {
      badges.push({ label: `To: ${filters.dateTo}`, key: 'dateTo' });
    }

    return badges;
  };

  const activeFilterBadges = getActiveFilterBadges();

  return (
    <div className={cn('space-y-4', className)}>
      <Form {...form}>
        {/* Quick Search and Basic Filters */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex flex-1 items-center gap-2">
            <div className="relative max-w-sm flex-1">
              <Search className="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
              <Input
                placeholder="Search outlets..."
                value={watchedValues.search || ''}
                onChange={e => handleFilterChange('search', e.target.value)}
                className="pl-9"
              />
            </div>

            <Select
              value={watchedValues.isActive || 'all'}
              onValueChange={value => handleFilterChange('isActive', value)}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
              <CollapsibleTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2">
                  <Filter className="h-4 w-4" />
                  Advanced Filters
                  {activeFiltersCount > 0 && (
                    <Badge variant="secondary" className="ml-1 h-5 px-1.5 text-xs">
                      {activeFiltersCount}
                    </Badge>
                  )}
                  {isAdvancedOpen ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
            </Collapsible>

            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFilters}
                className="text-muted-foreground hover:text-foreground gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Clear
              </Button>
            )}
          </div>
        </div>

        {/* Active Filter Badges */}
        {activeFilterBadges.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {activeFilterBadges.map(badge => (
              <Badge key={badge.key} variant="secondary" className="gap-1 pr-1">
                {badge.label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0.5 hover:bg-transparent"
                  onClick={() => {
                    if (badge.key === 'search') handleFilterChange('search', '');
                    else if (badge.key === 'isActive') handleFilterChange('isActive', 'all');
                    else if (badge.key === 'managerId') handleFilterChange('managerId', '');
                    else if (badge.key === 'dateFrom') handleFilterChange('dateFrom', undefined);
                    else if (badge.key === 'dateTo') handleFilterChange('dateTo', undefined);
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        )}

        {/* Advanced Filters Panel */}
        <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
          <CollapsibleContent className="space-y-4">
            <div className="bg-muted/50 rounded-lg border p-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                {/* Manager Filter */}
                <FormField
                  control={form.control}
                  name="managerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Manager
                      </FormLabel>
                      <Select
                        value={field.value || ''}
                        onValueChange={value => handleFilterChange('managerId', value)}
                        disabled={usersLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select manager" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">All Managers</SelectItem>
                          {managerOptions.map(user => (
                            <SelectItem key={user.userId} value={user.userId.toString()}>
                              {user.fullName} ({user.role})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Date From Filter */}
                <FormField
                  control={form.control}
                  name="dateFrom"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Created From
                      </FormLabel>
                      <Popover open={showDateFromPicker} onOpenChange={setShowDateFromPicker}>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                              <Calendar className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <CalendarComponent
                            mode="single"
                            selected={field.value}
                            onSelect={date => {
                              handleFilterChange('dateFrom', date);
                              setShowDateFromPicker(false);
                            }}
                            disabled={date => date > new Date() || date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Date To Filter */}
                <FormField
                  control={form.control}
                  name="dateTo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Created To
                      </FormLabel>
                      <Popover open={showDateToPicker} onOpenChange={setShowDateToPicker}>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                              <Calendar className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <CalendarComponent
                            mode="single"
                            selected={field.value}
                            onSelect={date => {
                              handleFilterChange('dateTo', date);
                              setShowDateToPicker(false);
                            }}
                            disabled={date => date > new Date() || date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </Form>
    </div>
  );
}
