import React from 'react';
import { Al<PERSON><PERSON><PERSON>gle, Building2, MapPin, Users } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/shared/components/ui/alert-dialog';
import { Badge } from '@/shared/components/ui/badge';
import { OutletWithManager } from '../types';

interface OutletDeleteDialogProps {
  outlet: OutletWithManager | null;
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
}

export function OutletDeleteDialog({
  outlet,
  open,
  onConfirm,
  onCancel,
  loading = false,
}: OutletDeleteDialogProps) {
  if (!outlet) return null;

  return (
    <AlertDialog open={open} onOpenChange={onCancel}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <AlertDialogTitle className="text-left">Delete Outlet</AlertDialogTitle>
              <AlertDialogDescription className="text-left">
                This action cannot be undone. This will permanently delete the outlet.
              </AlertDialogDescription>
            </div>
          </div>
        </AlertDialogHeader>

        {/* Outlet Details */}
        <div className="space-y-4 py-4">
          <div className="rounded-lg border bg-gray-50 p-4 space-y-3">
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4 text-gray-600" />
              <span className="font-medium text-gray-900">{outlet.name}</span>
              <Badge variant={outlet.isActive ? 'default' : 'secondary'}>
                {outlet.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            
            {outlet.address && (
              <div className="flex items-start gap-2 text-sm text-gray-600">
                <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <span>{outlet.address}</span>
              </div>
            )}

            {outlet.manager && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Users className="h-4 w-4" />
                <span>Manager: {outlet.manager.fullName}</span>
              </div>
            )}
          </div>

          <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-amber-800">
                  Warning: Data Loss
                </p>
                <p className="text-sm text-amber-700">
                  Deleting this outlet will also remove:
                </p>
                <ul className="text-sm text-amber-700 list-disc list-inside space-y-1 mt-2">
                  <li>All associated sales records</li>
                  <li>User assignments to this outlet</li>
                  <li>Historical performance data</li>
                  <li>Any related reports and analytics</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <AlertDialogFooter className="gap-2">
          <AlertDialogCancel 
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={loading}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {loading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Deleting...
              </>
            ) : (
              'Delete Outlet'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
