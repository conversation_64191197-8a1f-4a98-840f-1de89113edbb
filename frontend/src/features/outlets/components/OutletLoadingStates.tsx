import React from 'react';
import { Loader2, <PERSON>f<PERSON><PERSON><PERSON>, AlertCircle, Building2 } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Skeleton } from '@/shared/components/ui/skeleton';
import { Button } from '@/shared/components/ui/button';
import { cn } from '@/lib/utils';

interface LoadingStateProps {
  message?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Main loading state for outlets page
 */
export const OutletLoadingState = React.memo<LoadingStateProps>(
  ({ message = 'Loading outlets...', className, size = 'md' }) => {
    const sizeClasses = {
      sm: 'w-6 h-6',
      md: 'w-8 h-8',
      lg: 'w-12 h-12',
    };

    return (
      <div
        className={cn('flex min-h-[400px] items-center justify-center', className)}
        role="status"
        aria-live="polite"
      >
        <div className="text-center">
          <Loader2
            className={cn('mx-auto mb-4 animate-spin text-blue-600', sizeClasses[size])}
            aria-hidden="true"
          />
          <p className="text-gray-600" id="loading-message">
            {message}
          </p>
          <span className="sr-only">Loading outlets data, please wait</span>
        </div>
      </div>
    );
  }
);

OutletLoadingState.displayName = 'OutletLoadingState';

/**
 * Inline loading spinner for smaller components
 */
export const InlineOutletLoadingState = React.memo<{
  message?: string;
  className?: string;
}>(({ message = 'Loading...', className }) => {
  return (
    <div className={cn('flex items-center gap-2', className)} role="status" aria-live="polite">
      <Loader2 className="h-4 w-4 animate-spin text-blue-600" aria-hidden="true" />
      <span className="text-sm text-gray-600">{message}</span>
    </div>
  );
});

InlineOutletLoadingState.displayName = 'InlineOutletLoadingState';

/**
 * Loading overlay for refresh operations
 */
export const OutletLoadingOverlay = React.memo<{
  isVisible: boolean;
  message?: string;
  className?: string;
}>(({ isVisible, message = 'Refreshing outlets...', className }) => {
  if (!isVisible) return null;

  return (
    <div
      className={cn(
        'absolute inset-0 z-10 flex items-center justify-center bg-white/80 backdrop-blur-sm',
        'transition-opacity duration-200',
        className
      )}
      role="status"
      aria-live="polite"
    >
      <div className="flex items-center gap-3 rounded-lg border bg-white px-4 py-3 shadow-lg">
        <RefreshCw className="h-5 w-5 animate-spin text-blue-600" aria-hidden="true" />
        <span className="text-sm font-medium text-slate-700">{message}</span>
      </div>
    </div>
  );
});

OutletLoadingOverlay.displayName = 'OutletLoadingOverlay';

/**
 * Skeleton for outlet table
 */
export const OutletTableSkeleton = React.memo(() => (
  <Card>
    <CardHeader>
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-9 w-24" />
      </div>
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        {/* Search and filters skeleton */}
        <div className="flex items-center gap-4">
          <Skeleton className="h-9 flex-1" />
          <Skeleton className="h-9 w-32" />
          <Skeleton className="h-9 w-24" />
        </div>

        {/* Table header skeleton */}
        <div className="flex items-center gap-4 border-b pb-2">
          <Skeleton className="h-4 w-6" />
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-16" />
        </div>

        {/* Table rows skeleton */}
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="flex items-center gap-4 py-3">
            <Skeleton className="h-4 w-6" />
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-3 w-3 rounded-full" />
            <Skeleton className="h-4 w-20" />
            <div className="flex gap-2">
              <Skeleton className="h-6 w-6" />
              <Skeleton className="h-6 w-6" />
              <Skeleton className="h-6 w-6" />
            </div>
          </div>
        ))}

        {/* Pagination skeleton */}
        <div className="flex items-center justify-between pt-4">
          <Skeleton className="h-4 w-32" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
));

OutletTableSkeleton.displayName = 'OutletTableSkeleton';

/**
 * Skeleton for outlet statistics cards
 */
export const OutletStatsSkeleton = React.memo(() => (
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    {Array.from({ length: 4 }).map((_, index) => (
      <Card key={index}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-4" />
        </CardHeader>
        <CardContent>
          <Skeleton className="mb-2 h-8 w-20" />
          <Skeleton className="h-3 w-32" />
        </CardContent>
      </Card>
    ))}
  </div>
));

OutletStatsSkeleton.displayName = 'OutletStatsSkeleton';

/**
 * Error state component for outlets
 */
interface OutletErrorStateProps {
  error: any;
  onRetry?: () => void;
  className?: string;
  showRetryButton?: boolean;
}

export const OutletErrorState = React.memo<OutletErrorStateProps>(
  ({ error, onRetry, className, showRetryButton = true }) => {
    const getErrorMessage = (error: any): string => {
      if (!error) return 'An unknown error occurred';

      if (error.code) {
        switch (error.code) {
          case 'NETWORK_ERROR':
            return 'Network connection failed. Please check your internet connection.';
          case 'VALIDATION_ERROR':
            return 'Invalid data received. Please try refreshing the page.';
          case 'HTTP_404':
            return 'Outlets data could not be found.';
          case 'HTTP_500':
            return 'Server error occurred. Please try again later.';
          default:
            return error.message || 'Failed to load outlets data';
        }
      }

      return error.message || 'Failed to load outlets data';
    };

    const errorMessage = getErrorMessage(error);

    return (
      <div
        className={cn('flex min-h-[400px] items-center justify-center', className)}
        role="alert"
        aria-live="assertive"
      >
        <div className="max-w-md text-center">
          <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" aria-hidden="true" />
          <h2 className="mb-2 text-xl font-semibold text-red-600">
            Failed to Load Outlets
          </h2>
          <p className="mb-4 text-gray-600" id="error-message">
            {errorMessage}
          </p>
          {showRetryButton && onRetry && (
            <Button
              onClick={onRetry}
              variant="outline"
              className="gap-2"
              aria-describedby="error-message"
            >
              <RefreshCw className="h-4 w-4" aria-hidden="true" />
              Try Again
            </Button>
          )}
        </div>
      </div>
    );
  }
);

OutletErrorState.displayName = 'OutletErrorState';

/**
 * Empty state component for when no outlets are available
 */
export const OutletEmptyState = React.memo<{
  title?: string;
  message?: string;
  className?: string;
  onCreateOutlet?: () => void;
}>(
  ({
    title = 'No Outlets Found',
    message = 'No outlets are available at this time.',
    className,
    onCreateOutlet,
  }) => {
    return (
      <div
        className={cn('flex min-h-[400px] items-center justify-center', className)}
        role="status"
      >
        <div className="text-center">
          <Building2 className="mx-auto mb-4 h-12 w-12 text-gray-400" aria-hidden="true" />
          <h2 className="mb-2 text-xl font-semibold text-slate-700">{title}</h2>
          <p className="mb-4 text-gray-600">{message}</p>
          {onCreateOutlet && (
            <Button onClick={onCreateOutlet} className="gap-2">
              <Building2 className="h-4 w-4" />
              Create First Outlet
            </Button>
          )}
        </div>
      </div>
    );
  }
);

OutletEmptyState.displayName = 'OutletEmptyState';

/**
 * Wrapper component that adds loading overlay to cards during refresh
 */
export const RefreshingOutletWrapper = React.memo<{
  isRefreshing: boolean;
  children: React.ReactNode;
  className?: string;
  overlayMessage?: string;
}>(({ isRefreshing, children, className, overlayMessage = 'Updating outlets...' }) => (
  <div className={cn('relative', className)}>
    {children}
    <OutletLoadingOverlay isVisible={isRefreshing} message={overlayMessage} />
  </div>
));

RefreshingOutletWrapper.displayName = 'RefreshingOutletWrapper';
