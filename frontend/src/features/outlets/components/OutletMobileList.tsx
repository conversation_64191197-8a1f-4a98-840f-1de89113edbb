import { useState, useMemo } from 'react';
import { ChevronLeft, ChevronRight, Grid, List, CheckSquare, Square } from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Separator } from '@/shared/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { OutletMobileCard } from './OutletMobileCard';
import { OutletWithManager } from '../types';

interface OutletMobileListProps {
  outlets: OutletWithManager[];
  loading?: boolean;
  onEdit?: (outlet: OutletWithManager) => void;
  onDelete?: (outlet: OutletWithManager) => void;
  onView?: (outlet: OutletWithManager) => void;
  onBulkDelete?: (outlets: OutletWithManager[]) => void;
  pagination?: {
    pageIndex: number;
    pageSize: number;
    totalPages: number;
    totalItems: number;
  };
  onPaginationChange?: (pagination: { pageIndex: number; pageSize: number }) => void;
  className?: string;
}

export function OutletMobileList({
  outlets,
  loading = false,
  onEdit,
  onDelete,
  onView,
  onBulkDelete,
  pagination,
  onPaginationChange,
  className,
}: OutletMobileListProps) {
  const [selectedOutlets, setSelectedOutlets] = useState<Set<number>>(new Set());
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');

  // Handle individual outlet selection
  const handleOutletSelect = (outlet: OutletWithManager, selected: boolean) => {
    const newSelected = new Set(selectedOutlets);
    if (selected) {
      newSelected.add(outlet.id);
    } else {
      newSelected.delete(outlet.id);
    }
    setSelectedOutlets(newSelected);
  };

  // Handle select all toggle
  const handleSelectAll = () => {
    if (selectedOutlets.size === outlets.length) {
      setSelectedOutlets(new Set());
    } else {
      setSelectedOutlets(new Set(outlets.map(outlet => outlet.id)));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    const outletsToDelete = outlets.filter(outlet => selectedOutlets.has(outlet.id));
    if (outletsToDelete.length > 0 && onBulkDelete) {
      onBulkDelete(outletsToDelete);
      setSelectedOutlets(new Set());
    }
  };

  // Get selected outlets for bulk operations
  const selectedOutletsList = useMemo(() => {
    return outlets.filter(outlet => selectedOutlets.has(outlet.id));
  }, [outlets, selectedOutlets]);

  const isAllSelected = outlets.length > 0 && selectedOutlets.size === outlets.length;
  const isPartiallySelected = selectedOutlets.size > 0 && selectedOutlets.size < outlets.length;

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div
            key={index}
            className="h-24 bg-muted/50 rounded-lg animate-pulse"
          />
        ))}
      </div>
    );
  }

  if (outlets.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-muted-foreground">No outlets found</div>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Mobile Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSelectAll}
            className="h-8 px-2"
          >
            {isAllSelected ? (
              <CheckSquare className="h-4 w-4" />
            ) : isPartiallySelected ? (
              <div className="h-4 w-4 border border-current bg-current/20 rounded-sm" />
            ) : (
              <Square className="h-4 w-4" />
            )}
            <span className="ml-2 text-sm">
              {selectedOutlets.size > 0 ? `${selectedOutlets.size} selected` : 'Select all'}
            </span>
          </Button>

          {selectedOutlets.size > 0 && onBulkDelete && (
            <>
              <Separator orientation="vertical" className="h-6" />
              <Button
                variant="destructive"
                size="sm"
                onClick={handleBulkDelete}
                className="h-8 text-xs"
              >
                Delete ({selectedOutlets.size})
              </Button>
            </>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="h-8 w-8 p-0"
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
            className="h-8 w-8 p-0"
          >
            <Grid className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Outlets List/Grid */}
      <div
        className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 sm:grid-cols-2 gap-4'
            : 'space-y-3'
        }
      >
        {outlets.map((outlet) => (
          <OutletMobileCard
            key={outlet.id}
            outlet={outlet}
            isSelected={selectedOutlets.has(outlet.id)}
            onSelect={(selected) => handleOutletSelect(outlet, selected)}
            onView={onView}
            onEdit={onEdit}
            onDelete={onDelete}
          />
        ))}
      </div>

      {/* Mobile Pagination */}
      {pagination && onPaginationChange && (
        <div className="mt-6 space-y-4">
          {/* Results Info */}
          <div className="text-center text-sm text-muted-foreground">
            Showing {pagination.pageIndex * pagination.pageSize + 1} to{' '}
            {Math.min((pagination.pageIndex + 1) * pagination.pageSize, pagination.totalItems)} of{' '}
            {pagination.totalItems} outlets
          </div>

          {/* Pagination Controls */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                onPaginationChange({
                  pageIndex: pagination.pageIndex - 1,
                  pageSize: pagination.pageSize,
                })
              }
              disabled={pagination.pageIndex === 0}
              className="flex-1 max-w-[120px]"
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </Button>

            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Page</span>
              <Badge variant="outline" className="px-2 py-1">
                {pagination.pageIndex + 1} of {pagination.totalPages}
              </Badge>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                onPaginationChange({
                  pageIndex: pagination.pageIndex + 1,
                  pageSize: pagination.pageSize,
                })
              }
              disabled={pagination.pageIndex >= pagination.totalPages - 1}
              className="flex-1 max-w-[120px]"
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>

          {/* Page Size Selector */}
          <div className="flex items-center justify-center gap-2">
            <span className="text-sm text-muted-foreground">Show</span>
            <Select
              value={pagination.pageSize.toString()}
              onValueChange={(value) =>
                onPaginationChange({
                  pageIndex: 0,
                  pageSize: parseInt(value),
                })
              }
            >
              <SelectTrigger className="w-20 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm text-muted-foreground">per page</span>
          </div>
        </div>
      )}
    </div>
  );
}
