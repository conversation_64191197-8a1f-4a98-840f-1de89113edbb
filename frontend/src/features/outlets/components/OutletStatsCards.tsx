import { Building2, Activity, DollarSign, TrendingUp } from 'lucide-react';
import { MetricCard, MetricCardProps } from '@/features/dashboard/components/shared/MetricCard';
import { OutletStatsSkeleton } from './OutletLoadingStates';
import {
  formatCurrency,
  formatNumber,
  calculatePercentage,
  formatGrowth,
} from '../utils/outletUtils';

interface OutletStatsCardsProps {
  stats?: {
    totalOutlets: number;
    activeOutlets: number;
    totalRevenue: number;
    averageRevenue: number;
  };
  loading?: boolean;
  className?: string;
}

/**
 * Outlet statistics cards component
 */
export function OutletStatsCards({ stats, loading = false, className }: OutletStatsCardsProps) {
  // Show skeleton while loading
  if (loading) {
    return <OutletStatsSkeleton />;
  }

  // Show disabled cards if no stats available
  if (!stats) {
    const disabledCards: MetricCardProps[] = [
      {
        title: 'Total Outlets',
        value: '-',
        icon: <Building2 className="h-4 w-4" />,
        disabled: true,
      },
      {
        title: 'Active Outlets',
        value: '-',
        icon: <Activity className="h-4 w-4" />,
        disabled: true,
      },
      {
        title: 'Total Revenue',
        value: '-',
        icon: <DollarSign className="h-4 w-4" />,
        disabled: true,
      },
      {
        title: 'Average Revenue',
        value: '-',
        icon: <TrendingUp className="h-4 w-4" />,
        disabled: true,
      },
    ];

    return (
      <div className={`grid gap-4 md:grid-cols-2 lg:grid-cols-4 ${className || ''}`}>
        {disabledCards.map((card, index) => (
          <MetricCard key={index} {...card} />
        ))}
      </div>
    );
  }

  // Destructure the actual stats data
  const { totalOutlets, activeOutlets, totalRevenue, averageRevenue } = stats;

  // Calculate derived metrics
  const inactiveOutlets = totalOutlets - activeOutlets;
  const activePercentage = calculatePercentage(activeOutlets, totalOutlets);
  const inactivePercentage = calculatePercentage(inactiveOutlets, totalOutlets);

  // Define metric cards with actual data
  const metricCards: MetricCardProps[] = [
    {
      title: 'Total Outlets',
      value: formatNumber(totalOutlets),
      icon: <Building2 className="h-4 w-4" />,
      description: 'All registered outlets',
    },
    {
      title: 'Active Outlets',
      value: formatNumber(activeOutlets),
      icon: <Activity className="h-4 w-4" />,
      change: `+${activePercentage}`,
      changeDescription: 'of total outlets',
      description: `${inactiveOutlets} inactive (${inactivePercentage})`,
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(totalRevenue),
      icon: <DollarSign className="h-4 w-4" />,
      description: 'Combined revenue from all outlets',
    },
    {
      title: 'Average Revenue',
      value: formatCurrency(averageRevenue),
      icon: <TrendingUp className="h-4 w-4" />,
      description: 'Per outlet average',
    },
  ];

  return (
    <div className={`grid gap-4 md:grid-cols-2 lg:grid-cols-4 ${className || ''}`}>
      {metricCards.map((card, index) => (
        <MetricCard key={index} {...card} />
      ))}
    </div>
  );
}

/**
 * Individual outlet statistics card for detailed view
 */
interface OutletDetailStatsProps {
  outletName: string;
  stats: {
    totalSales: number;
    totalRevenue: number;
    averageOrderValue: number;
    salesCount: number;
    lastSaleDate?: string;
    monthlyGrowth: number;
  };
  loading?: boolean;
  className?: string;
}

export function OutletDetailStats({
  outletName,
  stats,
  loading = false,
  className,
}: OutletDetailStatsProps) {
  if (loading) {
    return <OutletStatsSkeleton />;
  }

  const detailCards: MetricCardProps[] = [
    {
      title: 'Total Sales',
      value: formatNumber(stats.totalSales),
      icon: <Building2 className="h-4 w-4" />,
      description: 'Number of completed sales',
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(stats.totalRevenue),
      icon: <DollarSign className="h-4 w-4" />,
      change: formatGrowth(stats.monthlyGrowth),
      changeDescription: 'from last month',
      description: `${stats.salesCount} transactions`,
    },
    {
      title: 'Average Order Value',
      value: formatCurrency(stats.averageOrderValue),
      icon: <TrendingUp className="h-4 w-4" />,
      description: 'Per transaction average',
    },
    {
      title: 'Last Sale',
      value: stats.lastSaleDate
        ? new Date(stats.lastSaleDate).toLocaleDateString()
        : 'No sales yet',
      icon: <Activity className="h-4 w-4" />,
      description: 'Most recent transaction',
    },
  ];

  return (
    <div className={className}>
      <h3 className="mb-4 text-lg font-semibold">{outletName} Statistics</h3>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {detailCards.map((card, index) => (
          <MetricCard key={index} {...card} />
        ))}
      </div>
    </div>
  );
}

/**
 * Compact outlet stats for use in tables or lists
 */
interface CompactOutletStatsProps {
  stats: {
    totalRevenue: number;
    salesCount: number;
    monthlyGrowth: number;
  };
  className?: string;
}

export function CompactOutletStats({ stats, className }: CompactOutletStatsProps) {
  const growthColor = stats.monthlyGrowth >= 0 ? 'text-green-600' : 'text-red-600';
  const growthIcon = stats.monthlyGrowth >= 0 ? '↗' : '↘';

  return (
    <div className={`space-y-1 text-sm ${className || ''}`}>
      <div className="flex items-center justify-between">
        <span className="text-gray-600">Revenue:</span>
        <span className="font-medium">{formatCurrency(stats.totalRevenue)}</span>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-gray-600">Sales:</span>
        <span className="font-medium">{formatNumber(stats.salesCount)}</span>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-gray-600">Growth:</span>
        <span className={`font-medium ${growthColor}`}>
          {growthIcon} {Math.abs(stats.monthlyGrowth).toFixed(1)}%
        </span>
      </div>
    </div>
  );
}

/**
 * Quick stats summary for dashboard widgets
 */
interface QuickOutletStatsProps {
  totalOutlets: number;
  activeOutlets: number;
  className?: string;
}

export function QuickOutletStats({
  totalOutlets,
  activeOutlets,
  className,
}: QuickOutletStatsProps) {
  const inactiveOutlets = totalOutlets - activeOutlets;
  const activePercentage = calculatePercentage(activeOutlets, totalOutlets);

  return (
    <div className={`flex items-center gap-4 text-sm ${className || ''}`}>
      <div className="flex items-center gap-2">
        <Building2 className="h-4 w-4 text-gray-500" />
        <span className="font-medium">{totalOutlets}</span>
        <span className="text-gray-600">Total</span>
      </div>
      <div className="flex items-center gap-2">
        <Activity className="h-4 w-4 text-green-500" />
        <span className="font-medium">{activeOutlets}</span>
        <span className="text-gray-600">Active ({activePercentage})</span>
      </div>
      {inactiveOutlets > 0 && (
        <div className="flex items-center gap-2">
          <div className="h-4 w-4 rounded-full bg-gray-400" />
          <span className="font-medium">{inactiveOutlets}</span>
          <span className="text-gray-600">Inactive</span>
        </div>
      )}
    </div>
  );
}
