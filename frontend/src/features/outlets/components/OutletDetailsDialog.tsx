import React from 'react';
import {
  Building2,
  MapPin,
  Phone,
  Mail,
  Clock,
  User,
  Calendar,
  Activity,
  FileText,
  X,
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/ui/dialog';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Separator } from '@/shared/components/ui/separator';
import { OutletWithManager } from '../types';
import { formatDate } from '@/shared/utils/date';

interface OutletDetailsDialogProps {
  outlet: OutletWithManager | null;
  open: boolean;
  onClose: () => void;
  onEdit?: (outlet: OutletWithManager) => void;
  onDelete?: (outlet: OutletWithManager) => void;
}

export function OutletDetailsDialog({
  outlet,
  open,
  onClose,
  onEdit,
  onDelete,
}: OutletDetailsDialogProps) {
  if (!outlet) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto sm:max-h-[85vh]">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="text-xl font-semibold">Outlet Details</DialogTitle>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header Section */}
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <Building2 className="h-6 w-6 text-blue-600" />
                <h2 className="text-2xl font-bold text-gray-900">{outlet.name}</h2>
                <Badge variant={outlet.isActive ? 'default' : 'secondary'}>
                  {outlet.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              {outlet.description && <p className="max-w-md text-gray-600">{outlet.description}</p>}
            </div>
          </div>

          <Separator />

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900">
              <Phone className="h-5 w-5" />
              Contact Information
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <MapPin className="mt-0.5 h-5 w-5 flex-shrink-0 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Address</p>
                    <p className="text-gray-600">{outlet.address}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Phone</p>
                    <p className="text-gray-600">{outlet.phoneNumber}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                {outlet.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">Email</p>
                      <p className="text-gray-600">{outlet.email}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Operating Hours</p>
                    <p className="text-gray-600">
                      {outlet.openingTime} - {outlet.closingTime}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Management Information */}
          <div className="space-y-4">
            <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900">
              <User className="h-5 w-5" />
              Management
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <User className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Manager</p>
                    <p className="text-gray-600">
                      {outlet.manager ? (
                        <span className="flex items-center gap-2">
                          {outlet.manager.fullName}
                          <Badge variant="outline" className="text-xs">
                            {outlet.manager.role}
                          </Badge>
                        </span>
                      ) : (
                        'No manager assigned'
                      )}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Activity className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Status</p>
                    <div className="flex items-center gap-2">
                      <div
                        className={`h-2 w-2 rounded-full ${
                          outlet.isActive ? 'bg-green-500' : 'bg-gray-400'
                        }`}
                      />
                      <span className="text-gray-600">
                        {outlet.isActive ? 'Currently Active' : 'Currently Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* System Information */}
          <div className="space-y-4">
            <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900">
              <Calendar className="h-5 w-5" />
              System Information
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Created</p>
                    <p className="text-gray-600">{formatDate(outlet.createdAt)}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Last Updated</p>
                    <p className="text-gray-600">{formatDate(outlet.updatedAt)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {outlet.description && (
            <>
              <Separator />
              <div className="space-y-4">
                <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900">
                  <FileText className="h-5 w-5" />
                  Description
                </h3>
                <div className="rounded-lg bg-gray-50 p-4">
                  <p className="whitespace-pre-wrap text-gray-700">{outlet.description}</p>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col items-stretch justify-end gap-3 border-t pt-6 sm:flex-row sm:items-center">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          {onEdit && (
            <Button
              variant="default"
              onClick={() => {
                onEdit(outlet);
                onClose();
              }}
            >
              Edit Outlet
            </Button>
          )}
          {onDelete && (
            <Button
              variant="destructive"
              onClick={() => {
                onDelete(outlet);
                onClose();
              }}
            >
              Delete Outlet
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
