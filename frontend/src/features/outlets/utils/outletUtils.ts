export const formatCurrency = (value: number | undefined | null): string => {
  // Input validation and type checking
  if (value === undefined || value === null) {
    console.warn('formatCurrency: received undefined or null value');
    return 'RM -';
  }

  if (typeof value !== 'number') {
    console.warn(`formatCurrency: received value: ${value} type: ${typeof value}`);
    return 'RM -';
  }

  if (isNaN(value)) {
    console.warn('formatCurrency: received NaN value');
    return 'RM -';
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'MYR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

export const formatNumber = (value: number | undefined | null): string => {
  // Input validation and type checking
  if (value === undefined || value === null) {
    console.warn('formatNumber: received undefined or null value');
    return '-';
  }

  if (typeof value !== 'number') {
    console.warn(`formatNumber: received value: ${value} type: ${typeof value}`);
    return '-';
  }

  if (isNaN(value)) {
    console.warn('formatNumber: received NaN value');
    return '-';
  }

  // Handle negative numbers
  if (value < 0) {
    return `-${formatNumber(Math.abs(value))}`;
  }

  // Format large numbers
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  }
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }

  // Return the number as string for values less than 1000
  return value.toString();
};

export const calculatePercentage = (value: number, total: number): string => {
  if (total === 0) return '0%';
  return `${Math.round((value / total) * 100)}%`;
};

export const formatGrowth = (growth: number): string => {
  const sign = growth >= 0 ? '+' : '';
  return `${sign}${growth.toFixed(1)}%`;
};

export const getGrowthColor = (growth: number): 'positive' | 'negative' | 'neutral' => {
  if (growth > 0) return 'positive';
  if (growth < 0) return 'negative';
  return 'neutral';
};