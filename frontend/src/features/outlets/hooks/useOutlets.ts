import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/shared/components/ui/use-toast';
import { outletApi } from '../services/outletApi';
import { QUERY_KEYS } from '@/shared/utils/constants';
import {
  OutletFilters,
  OutletsListResponse,
  Outlet,
  OutletDetails,
  OutletStats,
  CreateOutletRequest,
  UpdateOutletRequest,
} from '../types';

// Query keys for consistent cache management
export const OUTLET_QUERY_KEYS = {
  all: [QUERY_KEYS.OUTLETS] as const,
  lists: () => [...OUTLET_QUERY_KEYS.all, 'list'] as const,
  list: (filters?: OutletFilters & { page?: number; limit?: number }) =>
    [...OUTLET_QUERY_KEYS.lists(), filters] as const,
  details: () => [...OUTLET_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...OUTLET_QUERY_KEYS.details(), id] as const,
  stats: () => [...OUTLET_QUERY_KEYS.all, 'stats'] as const,
  statsSummary: () => [...OUTLET_QUERY_KEYS.stats(), 'summary'] as const,
  outletStats: (id: string, period?: string) =>
    [...OUTLET_QUERY_KEYS.stats(), id, period] as const,
} as const;

// Get outlets list with pagination and filters
export function useOutlets(filters?: OutletFilters & { page?: number; limit?: number }) {
  return useQuery({
    queryKey: OUTLET_QUERY_KEYS.list(filters),
    queryFn: () => outletApi.getOutlets(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

// Get single outlet with details
export function useOutlet(id: string) {
  return useQuery({
    queryKey: OUTLET_QUERY_KEYS.detail(id),
    queryFn: () => outletApi.getOutlet(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

// Get outlet by user's outletId (for header display)
export function useUserOutlet(outletId: number | null) {
  return useQuery({
    queryKey: OUTLET_QUERY_KEYS.detail(outletId?.toString() || ''),
    queryFn: () => outletApi.getOutlet(outletId!.toString()),
    enabled: !!outletId,
    staleTime: 10 * 60 * 1000, // 10 minutes - outlet info doesn't change often
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  });
}

// Get active outlets only
export function useActiveOutlets() {
  return useQuery({
    queryKey: OUTLET_QUERY_KEYS.list({ isActive: true }),
    queryFn: () => outletApi.getOutlets({ isActive: true }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

// Get all outlets (simplified for dropdowns/selects)
export function useAllOutlets() {
  return useQuery({
    queryKey: OUTLET_QUERY_KEYS.list({ limit: 1000 }),
    queryFn: () => outletApi.getAllOutlets(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  });
}

// Get outlets statistics summary
export function useOutletStatsSummary() {
  return useQuery({
    queryKey: OUTLET_QUERY_KEYS.statsSummary(),
    queryFn: () => outletApi.getOutletStatsSummary(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

// Get outlet statistics for a specific outlet
export function useOutletStats(id: string, period?: string) {
  return useQuery({
    queryKey: OUTLET_QUERY_KEYS.outletStats(id, period),
    queryFn: () => outletApi.getOutletStats(id, period),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

// Create outlet mutation
export function useCreateOutlet() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOutletRequest) => outletApi.createOutlet(data),
    onSuccess: (newOutlet) => {
      // Invalidate and refetch outlets list
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.statsSummary() });

      toast.success('Outlet Created', {
        description: `Outlet "${newOutlet.name}" has been created successfully.`,
      });
    },
    onError: (error: any) => {
      toast.error('Failed to Create Outlet', {
        description: error.response?.data?.message || 'Failed to create outlet. Please try again.',
      });
    },
  });
}

// Update outlet mutation
export function useUpdateOutlet() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateOutletRequest }) =>
      outletApi.updateOutlet(id, data),
    onSuccess: (updatedOutlet, { id }) => {
      // Update the outlet in the cache
      queryClient.setQueryData(
        OUTLET_QUERY_KEYS.detail(id),
        updatedOutlet
      );

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.statsSummary() });

      toast.success('Outlet Updated', {
        description: `Outlet "${updatedOutlet.name}" has been updated successfully.`,
      });
    },
    onError: (error: any) => {
      toast.error('Failed to Update Outlet', {
        description: error.response?.data?.message || 'Failed to update outlet. Please try again.',
      });
    },
  });
}

// Delete outlet mutation
export function useDeleteOutlet() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => outletApi.deleteOutlet(id),
    onSuccess: (_, id) => {
      // Remove the outlet from cache
      queryClient.removeQueries({ queryKey: OUTLET_QUERY_KEYS.detail(id) });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.statsSummary() });

      toast.success('Outlet Deleted', {
        description: 'Outlet has been deleted successfully.',
      });
    },
    onError: (error: any) => {
      toast.error('Failed to Delete Outlet', {
        description: error.response?.data?.message || 'Failed to delete outlet. Please try again.',
      });
    },
  });
}

// Bulk delete outlets mutation
export function useBulkDeleteOutlets() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => outletApi.bulkDeleteOutlets(ids),
    onSuccess: (_, ids) => {
      // Remove outlets from cache
      ids.forEach(id => {
        queryClient.removeQueries({ queryKey: OUTLET_QUERY_KEYS.detail(id) });
      });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.statsSummary() });

      toast.success('Outlets Deleted', {
        description: `${ids.length} outlet(s) have been deleted successfully.`,
      });
    },
    onError: (error: any) => {
      toast.error('Failed to Delete Outlets', {
        description: error.response?.data?.message || 'Failed to delete outlets. Please try again.',
      });
    },
  });
}

// Activate outlet mutation
export function useActivateOutlet() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => outletApi.activateOutlet(id),
    onSuccess: (updatedOutlet, id) => {
      // Update the outlet in the cache
      queryClient.setQueryData(
        OUTLET_QUERY_KEYS.detail(id),
        updatedOutlet
      );

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.statsSummary() });

      toast.success('Outlet Activated', {
        description: `Outlet "${updatedOutlet.name}" has been activated.`,
      });
    },
    onError: (error: any) => {
      toast.error('Failed to Activate Outlet', {
        description: error.response?.data?.message || 'Failed to activate outlet. Please try again.',
      });
    },
  });
}

// Deactivate outlet mutation
export function useDeactivateOutlet() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => outletApi.deactivateOutlet(id),
    onSuccess: (updatedOutlet, id) => {
      // Update the outlet in the cache
      queryClient.setQueryData(
        OUTLET_QUERY_KEYS.detail(id),
        updatedOutlet
      );

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: OUTLET_QUERY_KEYS.statsSummary() });

      toast.success('Outlet Deactivated', {
        description: `Outlet "${updatedOutlet.name}" has been deactivated.`,
      });
    },
    onError: (error: any) => {
      toast.error('Failed to Deactivate Outlet', {
        description: error.response?.data?.message || 'Failed to deactivate outlet. Please try again.',
      });
    },
  });
}
